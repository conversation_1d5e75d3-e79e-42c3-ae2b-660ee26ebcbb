{"ClusterStackName": "dev-cpu-amrpwl-ecs", "AppName": "post-image-service", "AppDesiredCount": "1", "AppMaxCount": "7", "AppMinCount": "1", "AutoScaleHighThreshold": "70", "AutoScaleLowThreshold": "40", "Priority": "442", "AppContainerPort": "8083", "CPUunits": "1024", "MemoryReserved": "1024", "AppCpu": "256", "ContainerTotalCpu": "512", "ContainerTotalMemory": "1024", "AppMemory": "512", "ImageProcessingJobsTableName": "dev-post-image-processor-dynamo-jobs", "ImageProcessingS3BucketName": "dev-l1-amrpwl-post-images", "Environment": "dev", "EnvironmentType": "nonprod", "PagerDutyURL": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue", "NetworkStackName": "AMRPWL-Dev", "KinesisForSplunkStackName": "nonprod-kinesissplunk", "MemoryReservationThreshold": "80", "HealthyHostThreshold": "1", "CPUHighThresholdPagerDuty": "90"}