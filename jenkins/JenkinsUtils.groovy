import groovy.json.JsonOutput
/**
 * Hosts the common utility functions for j<PERSON>kins pipelines
 */

/**
 * Get the CFN stack name
 * @param  env [environment name]
 * @return     [CFN stack name wrt environment name]
 */
String getCFNStackName(String env) {
  return "${env}-change-me"
}

/**
 * get the name of the repository
 * @return [Name of the repository]
 */
String getRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}

/**
 * Read json file and return as String required by aws cli
 * @param  filename          [Name of the file]
 * @param  ['ParameterKey'   [Key ]
 * @param  'ParameterValue'] [Value]
 * @return                   [String of key values pairs]
 */
def paramsFromFile(String filename, keyPair = ['ParameterKey', 'ParameterValue']) {
  assert keyPair.size() == 2

  def paramsJson = readJSON(file: filename)

  paramsJson.collect { item ->
    keyPair.collect { key ->
      item.get(key)
    }.join('=')
  }.join(' ')

}

/**
 * Copies the default images to an s3 bucket
 */
void copyDefaultImagesToS3(String envName) {
  sh """
    # copy default-images to s3 bucket
    aws s3 cp default-images/ s3://${envName}-l1-amrpwl-post-images/default-images/ --recursive
  """
}


def paramsFromKeyValuePairsFromFile(String filename) {
  def paramsJson = readJSON(file: filename)
  paramsJson.collect { 
    item -> "${item}"
  }.join(" ")
}

void deployResourceStack(String env) {
  echo "--Generate Post Image Service Resources on AWS for ${env}---"
  def stackName = "${env}-post-image-service-resources"
  def parameterOverrides = paramsFromKeyValuePairsFromFile("env/${env}/${env}.resources.params.json")
  def tags = paramsFromKeyValuePairsFromFile("env/${env}/${env}.tags.json")
  def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/post-image-resources.yml --parameter-overrides ${parameterOverrides} --no-fail-on-empty-changeset --capabilities CAPABILITY_NAMED_IAM --tags ${tags}"

  echo "Running this command"
  echo "${args}"
  echo "Creating Post Image Service Resoures Cluster for ${env}"
  sh "aws cloudformation ${args}"
}


return this
