import groovy.json.JsonOutput

def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null

// Jenkins deployment pipeline
pipeline {
  agent { label 'aws-ec2'}

  stages {
    stage('Deploying to Dev.') {
      agent {
        docker {
                  image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
                  args '-u root:root'
                }
      }
      steps {
        echo "Attempting to deploy to Dev"
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
        }
        echo "BUILD_VERSION: ${params.BUILD_VERSION}"

        withCredentials([[
          $class: "AmazonWebServicesCredentialsBinding",
          credentialsId: "dev-amrpwl-aws-deployer"
        ]]) {
          script {
            jenkinsUtils.deployResourceStack("dev")
          }
          sh "jenkins/scripts/jenkins-deploy.sh dev ${params.BUILD_VERSION}"
        }
        stash 'project'
      }
      post {
        success {
          echo 'success! Deployed to dev.'
        }
        failure {
          echo "failed to deploy to dev."
        }
        aborted {
          echo "job aborted. Did not deploy to dev."
        }
      }
    }
    stage('Deploy to INT?') {
      agent none
      steps {
        input(message: "Do you want to deploy version ${params.BUILD_VERSION} to INT?")
      }
      post {
        success {
          echo 'Attempting to deploy to INT'
        }
        aborted {
          echo "job aborted. Aborting attempt to deploy to INT"
        }
      }
    }
    stage('Deploying to INT') {
      agent {
        docker {
                  image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
                  args '-u root:root'
                }
      }
      steps {
        echo "Attempting to deploy to INT"
        echo "BUILD_VERSION: ${params.BUILD_VERSION}"

        withCredentials([[
          $class: "AmazonWebServicesCredentialsBinding",
          credentialsId: "nonprod-amrpwl-aws-deployer"
        ]]) {
          script {
            jenkinsUtils.deployResourceStack("int")
          }
          sh "jenkins/scripts/jenkins-deploy.sh int ${params.BUILD_VERSION}"
        }
        stash 'project'
      }
      post {
        success {
          echo 'success! Deployed to INT.'
        }
        failure {
          echo "failed to deploy to INT."
        }
        aborted {
          echo "job aborted. Did not deploy to INT."
        }
      }
    }
    stage('Deploy to UAT?') {
      agent none
      steps {
        input(message: "Do you want to deploy version ${params.BUILD_VERSION} to UAT?")
      }
      post {
        success {
          echo 'Attempting to deploy to uat'
        }
        aborted {
          echo "job aborted. Aborting attempt to deploy to uat"
        }
      }
    }

    stage('Deploying to UAT') {
      agent {
        docker {
                  image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
                  args '-u root:root'
                }
      }
      steps {
        echo "Attempting to deploy to UAT"
        echo "BUILD_VERSION: ${params.BUILD_VERSION}"

        withCredentials([[
          $class: "AmazonWebServicesCredentialsBinding",
          credentialsId: "uat-amrpwl-aws-deployer"
        ]]) {
          script {
            jenkinsUtils.deployResourceStack("uat")
          }
          sh "jenkins/scripts/jenkins-deploy.sh uat ${params.BUILD_VERSION}"
        }
        stash 'project'
      }
      post {
        success {
          echo 'success! Deployed to UAT.'
        }
        failure {
          echo "failed to deploy to UAT."
        }
        aborted {
          echo "job aborted. Did not deploy to UAT."
        }
      }
    }
  }
}
