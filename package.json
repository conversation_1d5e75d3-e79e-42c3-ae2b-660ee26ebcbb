{"name": "post-image-service", "version": "0.1.246-SNAPSHOT", "description": "Image Service - handles processing of the images that's uploaded for partner offers", "main": "main.js", "scripts": {"test": "npx gulp test", "start": "node build/src/main.js", "build": "npx gulp build", "dist": "npx gulp dist", "docker-build": "yarn dist && NAME=post-image-service docker-build", "docker-publish": "yarn docker-build && NAME=post-image-service docker-push", "apidocs:generate": "npx ./node_modules/apidoc/bin/apidoc -f \".*\\.ts$\"  --output docs/"}, "releaseme": {"steps": ["setReleaseVersion", "commitReleaseVersion", "tagRelease", "setNextVersion", "commitNextVersion", "pushChanges"]}, "docker-registry": "277983268692.dkr.ecr.us-east-1.amazonaws.com", "repository": {"type": "git", "url": "git+https://github.com/LoyaltyOne/post-image-service.git"}, "husky": {"hooks": {"pre-push": "npm run test && gulp tslint "}}, "author": "", "license": "ISC", "dependencies": {"adm-zip": "^0.4.13", "apidoc": "^0.17.7", "aws-sdk": "^2.396.0", "bunyan": "^1.8.12", "convict": "^4.4.1", "file-type": "^10.7.1", "image-size": "^0.7.3", "inversify": "^5.0.1", "is-svg": "^4.1.0", "lodash": "^4.17.11", "path": "^0.12.7", "reflect-metadata": "^0.1.13", "request": "^2.88.0", "request-promise": "^4.2.2", "restify": "^11.1.0", "restify-cors-middleware2": "^2.2.1", "restify-errors": "^8.0.2", "restify-router": "^0.6.2", "semver": "^5.6.0", "tsc": "^1.20150623.0", "tslint": "^5.12.0", "typedi": "^0.8.0", "uuid": "^3.3.2", "verror": "^1.10.0"}, "devDependencies": {"@types/adm-zip": "^0.4.32", "@types/bunyan": "^1.8.5", "@types/chai": "^4.1.7", "@types/file-type": "^10.6.0", "@types/image-size": "^0.7.0", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.5", "@types/node": "^10.12.19", "@types/request-promise": "^4.1.42", "@types/restify-errors": "^4.3.3", "@types/sinon": "^7.0.5", "@types/uuid": "^3.4.4", "chai": "^4.2.0", "del": "^3.0.0", "docker-build-run-push": "^3.0.0", "gulp": "^5.0.0", "gulp-install": "^1.1.0", "gulp-load-plugins": "^1.5.0", "gulp-mocha": "^8.0.0", "gulp-tslint": "^8.1.3", "gulp-typescript": "^5.0.0", "husky": "^1.3.1", "rimraf": "^2.6.3", "sequelize-cli": "^5.4.0", "sinon": "^7.2.3", "ts-node": "^8.0.2", "tslint-config-prettier": "^1.17.0", "typescript": "^3.2.2"}}