export enum AllowedOptimizationTypes {
  w = "w",
  h = "h",
  ar = "ar",
  fit = "fit",
  pad = "pad",
  fm = "fm",
  bg = "bg",
  dpr = "dpr",
  preserve_animation = "preserve_animation"
}

/**
 * List of options available via Imgix
 * Note: This is not a complete list
 */
export interface IOptimizations {
  w?: number;
  h?: number;
  ar?: string;
  fit?: string;
  pad?: string;
  fm?: string;
  bg?: string;
  dpr?: number;
  preserve_animation?: boolean;
}

export interface IProcessImagesRequest {
  zip_file?: IFileProcessingZipData;
  external_images?: IFileProcessingData[];
}

export interface IFileProcessingData {
  location: string;
  optimizations?: IOptimizations;
}
export interface IFileProcessingZipData {
  location: string;
  optimizations?: IOptimizations;
  files_to_process?: string[];
}
