import { Service } from "typedi";
import { Config } from "../config/config";
import {
  IProcessedFileMetadata,
  IZipMetadata
} from "../interfaces/IFileMetadata";
import {
  IProcessImagesRequest,
  IFileProcessingData,
  IOptimizations
} from "../interfaces/IProcessImagesRequest";
import * as fileUtils from "../utils/file.utils";
import * as dbUtils from "../utils/dynamoDb.utils";
import * as uuid from "uuid";
import errors = require("restify-errors");
import {
  API_ERROR_CODES,
  apiErrorWrapper,
  IMAGE_PROCESSING_STATUS,
  PROCESSING_ERROR_CODES
} from "../constants";
import { logger } from "../log";
import * as zip from "adm-zip";
import { get } from "lodash";
import * as fileType from 'file-type';
@Service("ProcessImagesService")
export class ProcessImagesService {
  private processingJobTableName: string;
  private offersImagesS3Bucket: string;
  private imageProcessorBasePath: string;
  private allowedMimeTypes: string[];
  private processedImageAttributes;
  private awsRegion: string;
  private filterableZipPrefixes: string[];

  constructor(private config: Config) {
    this.processingJobTableName = this.config.config.get(
      "imageProcessingJobsTableName"
    );
    this.offersImagesS3Bucket = this.config.config.get("offersImagesS3Bucket");
    this.imageProcessorBasePath = this.config.config.get(
      "imageProcessorBasePath"
    );
    this.allowedMimeTypes = this.config.config.get("allowedMimeTypes");
    this.processedImageAttributes = this.config.config.get(
      "processedImageAttributes"
    );
    this.awsRegion = this.config.config.get("awsRegion");
    this.filterableZipPrefixes = this.config.config.get(
      "zipFilePrefixesToIgnore"
    );
  }

  get name() {
    return "ProcessImagesService";
  }

  // Initiates the processing of the requested images jobs by saving the UUID in Dynamo
  // and returns back the job id back to the caller
  public async startImageProcessingJob(request): Promise<string> {
    const jobId: string = uuid.v4();

    // Synchronously wait until the job has been recorded in the table, before returning the id
    await dbUtils.addNewJob(this.awsRegion, this.processingJobTableName, {
      id: jobId,
      jobStatus: IMAGE_PROCESSING_STATUS.INCOMPLETE,
    });

    // Asynchronously start processing of the images. Don't wait for it to be done on purpose.
    this.processRequest(request, jobId);
    return jobId;
  }

  public async getJobResponse(jobId: string) {
    const getJobResponse = await dbUtils.getItem(
      this.awsRegion,
      this.processingJobTableName,
      jobId
    );

    // Return the
    if (getJobResponse.Item === undefined || getJobResponse.Item === null) {
      throw new errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.PROCESSING_JOB_NOT_FOUND)
      );
    }

    if (getJobResponse.Item.jobStatus !== IMAGE_PROCESSING_STATUS.DONE) {
      throw new errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.PROCESSING_JOB_NOT_DONE)
      );
    }

    return getJobResponse.Item.jobResponse;
  }

  public async processRequest(
    request: IProcessImagesRequest,
    jobId: string
  ): Promise<void> {
    logger.info("Starting processing job with the id " + jobId);

    const response: { [key: string]: any } = {};

    // If any of the images fail during the processing, we want to enable clean up of all objects
    // that were uploaded to S3
    let cleanupRequired: boolean = false;

    if (request.external_images) {
      for (const data of request.external_images) {
        let processedMetadata: IProcessedFileMetadata;
        processedMetadata = await this.processImage(
          data.location,
          data.optimizations
        );

        if (!processedMetadata.success) {
          cleanupRequired = true;
        }
        response[data.location] = processedMetadata;
      }
    }

    // If there is a ZIP file location, process images inside the zip as well
    if (request.zip_file) {
      // Unpack the zip first in order to get the information and buffers of the files that will need to be processed
      const zipMetadata: IZipMetadata = await this.unpackZip(
        request.zip_file.location
      );

      // Add additional information into the response regarding the state of the unpacking zip itself. We want to
      // show the client that zip has failed to unpack/process midways if something went wrong
      response.zip_file_processing_metadata = zipMetadata.processingMetadata;

      // Store the array of files to process
      const filesToProcess = get(request.zip_file, "files_to_process");

      // For each file inside the zip, process the files
      if (filesToProcess) {
        for (const fileName of filesToProcess) {
          const fileData = zipMetadata.zipFiles.get(fileName);

          let processedMetadata: IProcessedFileMetadata;
          if (fileData) {
            processedMetadata = await this.processImage(
              fileName,
              request.zip_file.optimizations,
              fileData
            );
          } else {
            logger.error("Image not included in zip " + fileName);
            processedMetadata = PROCESSING_ERROR_CODES.IMAGE_NOT_FOUND_IN_ZIP;
          }

          if (!processedMetadata.success) {
            cleanupRequired = true;
          }
          response[fileName] = processedMetadata;
        }
      } else {
        for (const [fileName, fileData] of zipMetadata.zipFiles) {
          let processedMetadata: IProcessedFileMetadata;

          processedMetadata = await this.processImage(
            fileName,
            request.zip_file.optimizations,
            fileData
          );
          if (!processedMetadata.success) {
            cleanupRequired = true;
          }
          response[fileName] = processedMetadata;
        }
      }
    }

    await dbUtils.updateJob(
      this.awsRegion,
      this.processingJobTableName,
      jobId,
      IMAGE_PROCESSING_STATUS.DONE,
      response
    );

    if (cleanupRequired) {
      await this.cleanProcessedObjects(response);
    }

    logger.info("Finished processing job with the id " + jobId);
  }

  // Returns metadata of the unpacking of zip and information about the files that are inside
  private async unpackZip(zipLink: string): Promise<IZipMetadata> {
    let rawZipBuffer: Buffer;
    const zipFiles: Map<string, Buffer> = new Map<string, Buffer>();

    try {
      rawZipBuffer = await fileUtils.retrieveFile(zipLink);
    } catch (exception) {
      logger.debug(exception);
      logger.error("Failed to retrieve zip file from the link " + zipLink);

      return {
        processingMetadata: PROCESSING_ERROR_CODES.UNABLE_TO_RETRIEVE,
        zipFiles,
      };
    }

    for (const entry of new zip(rawZipBuffer).getEntries()) {
      const entryName = entry.entryName;

      // Filtering out entries that might have been added by the ZIP'ping tool. Folders like '__MACOSX/' will be ignored on purpose
      if (
        !this.filterableZipPrefixes.find((prefix) =>
          entryName.startsWith(prefix)
        )
      ) {
        zipFiles.set(entryName, entry.getData());
      }
    }

    return {
      processingMetadata: {
        success: true,
      },
      zipFiles,
    };
  }

  private async cleanProcessedObjects(response) {
    const objectsToCleanup = [];

    // Go through every key and remove any s3 objects that might exist in the response
    for (const key of Object.keys(response)) {
      if (response[key].raw_s3_location) {
        objectsToCleanup.push(
          this.extractS3KeyFromUrl(response[key].raw_s3_location)
        );
      }
      if (response[key].processed_s3_location) {
        objectsToCleanup.push(
          this.extractS3KeyFromUrl(response[key].processed_s3_location)
        );
      }
    }

    if (objectsToCleanup.length > 0) {
      await fileUtils.deleteObjectsFromS3(
        this.awsRegion,
        this.offersImagesS3Bucket,
        objectsToCleanup
      );
    }
  }

  private extractS3KeyFromUrl(s3Url: string) {
    const awsS3UrlPrefix = this.offersImagesS3Bucket + "/";
    return s3Url.substring(
      s3Url.indexOf(awsS3UrlPrefix) + awsS3UrlPrefix.length
    );
  }

  // TODO: take an optional optimization param
  private async processImage(
    link: string,
    optimizations?: IOptimizations,
    buffer?: Buffer
  ): Promise<IProcessedFileMetadata> {
    let rawImageBuffer: Buffer;

    // Retrieve the image from the external link if not provided
    if (!buffer) {
      try {
        rawImageBuffer = await fileUtils.retrieveFile(link);
      } catch (exception) {
        logger.debug(exception);
        logger.error("Failed to retrieve image from the link " + link);

        return PROCESSING_ERROR_CODES.UNABLE_TO_RETRIEVE;
      }
    } else {
      rawImageBuffer = buffer;
    }

    try {
      // Ensure the MIME Type is allowed before processing the image
      if (!fileUtils.isAllowedMimeType(rawImageBuffer, this.allowedMimeTypes)) {
        logger.error("Failed mime check for the link " + link);
        return PROCESSING_ERROR_CODES.INVALID_MIME_TYPE;
      }

      // Ensure the original image complied the minimum width and height values
      if (
        !fileUtils.hasValidDimensions(
          rawImageBuffer,
          this.processedImageAttributes.minWidth,
          this.processedImageAttributes.minHeight
        )
      ) {
        logger.error("Image has invalid dimensions");
        return PROCESSING_ERROR_CODES.INVALID_IMAGE_DIMENSIONS;
      }

      // Upload the raw image to S3, so the it can be processed
      const rawImageS3Key: string = "raw-images/" + uuid.v4();
      logger.info(
        "DEBUG - Upload Starting - Uploading raw file " +
          link +
          " to S3 key " +
          rawImageS3Key
      );
      await fileUtils.uploadToS3(
        this.awsRegion,
        rawImageBuffer,
        this.offersImagesS3Bucket,
        rawImageS3Key
      );
      logger.info(
        "DEBUG - Uploaded raw file " + link + " to S3 key " + rawImageS3Key
      );

      await fileUtils.waitForFileToExist(
        this.awsRegion,
        this.offersImagesS3Bucket,
        rawImageS3Key
      );

      // Check if the file is a GIF
      const fileInfo = fileType(rawImageBuffer);
      const isGif = fileInfo && fileInfo.ext === "gif";

      let processedImage: Buffer;

      if (isGif) {
        // For GIFs, skip imgix processing to preserve animation
        processedImage = rawImageBuffer;
        logger.info(
          "DEBUG - Skipping imgix processing for GIF to preserve animation"
        );
      } else {
        // For non-GIFs, use imgix as before
        // Construct an image processing URL that will crop the image to the desired width and height while maintaining
        // the original ratio. The desired dimensions are determined based on the original image and modified to
        // pad the image where necessary to create a square. I.E if the original image has dimensions w=400 and height=600, the processed image
        // will have dimensions w=600 and height=600, while maintaining the aspect ratio of the original image and padding out where necessary.

        const imageProcessorURL: string =
          this.imageProcessorBasePath +
          rawImageS3Key +
          this.generateImgixQueryParams(rawImageBuffer, optimizations);

        logger.info(
          "DEBUG - Requesting image from processor with the url " +
            imageProcessorURL
        );

        const maxTries = 5;
        let tries = 0;
        let success = false;

        while (tries < maxTries && !success) {
          tries++;
          try {
            processedImage = await fileUtils.retrieveFile(imageProcessorURL);
            logger.info(
              "DEBUG - Image has been resized " +
                link +
                " s3 path = " +
                rawImageS3Key +
                "processorURL = " +
                imageProcessorURL
            );
            success = true;
          } catch (exception) {
            logger.error(
              "DEBUG - Exception:" +
                exception +
                " with " +
                link +
                " s3 path = " +
                rawImageS3Key +
                " processorURL = " +
                imageProcessorURL
            );
            await new Promise((resolve) => setTimeout(resolve, 30000));
          }
        }

        // Ensure that after image kit does its optimizations and resizing, it complies minimum image dimensions
        if (
          !fileUtils.hasValidDimensions(
            processedImage,
            this.processedImageAttributes.minWidth,
            this.processedImageAttributes.minHeight
          )
        ) {
          logger.error("Image has invalid dimensions after processing");
          return PROCESSING_ERROR_CODES.INVALID_DIMENSIONS_AFTER_RESIZING;
        }
      }

      // Save the processed image in S3 and return the information
      const processedImageS3Key: string = "processed-images/" + uuid.v4();
      await fileUtils.uploadToS3(
        this.awsRegion,
        processedImage,
        this.offersImagesS3Bucket,
        processedImageS3Key
      );
      logger.info("Uploaded processed file to S3 key " + processedImageS3Key);

      const s3BaseURI = "https://s3.amazonaws.com/";

      return {
        raw_s3_location:
          s3BaseURI + this.offersImagesS3Bucket + "/" + rawImageS3Key,
        processed_s3_location:
          s3BaseURI + this.offersImagesS3Bucket + "/" + processedImageS3Key,
        success: true,
      };
    } catch (exception) {
      logger.error(exception);

      return PROCESSING_ERROR_CODES.UNEXPECTED_ERROR;
    }
  }

  private generateImgixQueryParams(
    buffer: Buffer,
    optimizations?: IOptimizations
  ) {
    const dimensions = fileUtils.getImageInfo(buffer);
    const length = Math.min(
      Math.max(dimensions.width, dimensions.height),
      this.processedImageAttributes.height
    );
    if (!optimizations || optimizations === undefined) {
      // f-jpg - convert file to jpg (Removed this convert to add it back do `&fm=jpg`)
      // w - width of the image
      // h - height of the image
      // cm-pag_resize - cropping option to center the image and pad the
      // remaining width or height based on the aspect ratio
      // bg-FFFFFF - background color for the image - only seen when padding applied
      // preserve_animation=true - preserve animation in the image
      return (
        "?w=" +
        length +
        "&h=" +
        length +
        "&pad=auto&fit=fill&bg=FFFFFF&preserve_animation=true"
      );
    }

    return (
      "?" +
      Object.keys(optimizations)
        .map((key) => key + "=" + optimizations[key])
        .join("&")
    );
  }
}
