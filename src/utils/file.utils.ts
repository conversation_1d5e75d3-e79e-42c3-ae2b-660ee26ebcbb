import request = require("request-promise");
import fileType = require("file-type");
import S3 = require("aws-sdk/clients/s3");
import { logger } from "../log";
import * as uuid from "uuid";
import imageSize = require("image-size");
import isSvg = require("is-svg");

export async function retrieveFile(fileLink: string): Promise<Buffer> {
  const options = {
    uri: fileLink,
    method: "GET",
    encoding: null
  };

  const response = await request.get(options);
  const buffer: Buffer = Buffer.from(response, "utf8");
  return buffer;
}

export function isAllowedMimeType(
  fileBuffer: Buffer,
  allowedMimeTypes: string[]
): boolean {
  // if the file is an svg then return fileMetadata object with the correct mime type
  // otherwise get the non-svg fileType metadata
  const fileMetadata = isSvg(fileBuffer)
    ? { mime: "image/svg+xml" }
    : fileType(fileBuffer);

  // In case if mime type was not able to be found, assume that it is not passing mime check
  return !fileMetadata || !fileMetadata.mime
    ? false
    : allowedMimeTypes.includes(fileMetadata.mime);
}

export async function uploadToS3(
  awsRegion: string,
  fileBuffer: Buffer,
  bucketName: string,
  destinationKey: string
) {
  const s3Client: S3 = new S3({ region: awsRegion });

  // if the file is an svg then return fileMetadata object with the correct mime type
  // otherwise get the non-svg fileType metadata
  const fileMetadata = isSvg(fileBuffer)
    ? { mime: "image/svg+xml" }
    : fileType(fileBuffer);

  const mime: string = fileMetadata.mime;
  await s3Client
    .upload({
      Bucket: bucketName,
      Key: destinationKey,
      Body: fileBuffer,
      ContentEncoding: mime,
      ContentType: mime
    })
    .promise();
}

export async function waitForFileToExist(
  awsRegion: string,
  bucketName: string,
  fileName: string,
) {
  const s3Client: S3 = new S3({ region: awsRegion });

  await s3Client.waitFor('objectExists', {
    Bucket: bucketName,
    Key: fileName,
    $waiter: {
      maxAttempts: 5,
      delay: 1
    }
  })
  .promise();
}

export async function deleteObjectsFromS3(
  awsRegion: string,
  bucketName: string,
  keys: string[]
) {
  const s3Client: S3 = new S3({ region: awsRegion });
  const params = {
    Bucket: bucketName,
    Delete: {
      Objects: keys.map(key => ({ Key: key })),
      Quiet: false
    }
  };

  logger.info(
    "Deleting with the following parameters " + JSON.stringify(params)
  );

  await s3Client.deleteObjects(params).promise();
}

export function hasValidDimensions(
  fileBuffer: Buffer,
  minWidth: number,
  minHeight: number
): boolean {
  const dimensions = imageSize(fileBuffer);
  logger.info("Image dimensions are " + JSON.stringify(dimensions));

  if (dimensions.width < minWidth && dimensions.height < minHeight) {
    return false;
  }

  return true;
}

/**
 * Generates a presigned URL where the object is going to be uploaded in while enforcing the mimeType
 * @param awsRegion region of the bucket where it resides
 * @param bucketName name of the bucket for which the presgined url is going to be generated for
 * @param key object's key for which the UUID is going to be appended to avoid collision
 * @param mimeType mimeType of the object that's going to be uploaded to the URL
 * @param expirationInSeconds expiration of the presigned URL
 */
export function generatePutObjectPresignedUrl(
  awsRegion: string,
  bucketName: string,
  key: string,
  mimeType: string,
  expirationInSeconds: number
): Promise<string> {
  const uniqueKey: string = "direct-uploads/" + key + "-" + uuid.v4();
  const s3Client: S3 = new S3({ region: awsRegion });
  const params = {
    Bucket: bucketName,
    Key: uniqueKey,
    Expires: expirationInSeconds,
    ContentType: mimeType
  };

  logger.info(
    "Initiating the generation of presigned url with the following params " +
      JSON.stringify(params)
  );

  return new Promise((resolve, reject) => {
    s3Client.getSignedUrl("putObject", params, (err, url) => {
      err ? reject(err) : resolve(url);
    });
  });
}

export const getImageInfo = imageSize;