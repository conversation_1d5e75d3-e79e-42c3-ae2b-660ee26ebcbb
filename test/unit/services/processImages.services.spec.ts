import { expect } from "chai";
import { ProcessImagesService } from "../../../src/services/processImages.service";
import * as uuid from "uuid";
import * as dbUtils from "../../../src/utils/dynamoDb.utils";
import * as fileUtils from "../../../src/utils/file.utils";
import * as fileType from 'file-type';

import {
  API_ERROR_CODES,
  apiErrorWrapper,
  IMAGE_PROCESSING_STATUS,
  PROCESSING_ERROR_CODES
} from "../../../src/constants";
import errors = require("restify-errors");
import fs = require("fs");

// import sinon = require('sinon');
const sinon = require("sinon");

describe("processImages.service", () => {
  let processimagesService: ProcessImagesService;
  let fakeConfig;
  let uuidStub;
  let dbUtilsStub;
  let fileUtilsStub;
  const testUuid = "test-uuid";
  let goodExternalLinkBuffer;
  let badMimeExternalLinkBuffer;
  let weirdLinkBuffer;
  let badDimensionsBuffer;
  let fileUtilsUploadToS3Stub;
  let fileUtilsWaitForFileToExist;
  let dbUtilsUpdateJobStub;
  let fileUtilsDeleteObjectsFromS3Stub;
  let dbUtilsGetItemJob;

  let fileTypeStub;
  let gifBuffer;

  before(() => {
    const fakeConfigMap = new Map();
    fakeConfigMap.set("imageProcessingJobsTableName", "tableName");
    fakeConfigMap.set("offersImagesS3Bucket", "bucketName");
    fakeConfigMap.set("imageProcessorBasePath", "imageProcessorPath");
    fakeConfigMap.set("allowedMimeTypes", ["png"]);
    fakeConfigMap.set("processedImageAttributes", {
      height: "700",
      width: "700"
    });
    fakeConfigMap.set("awsRegion", "us-west-2");
    fakeConfigMap.set("zipFilePrefixesToIgnore", ["__MACOSX/"]);

    fakeConfig = sinon.fake();
    fakeConfig.config = fakeConfigMap;

    processimagesService = new ProcessImagesService(fakeConfig);

    uuidStub = sinon.stub(uuid, "v4").callsFake(() => testUuid);

    // Stub dynamodb calls
    sinon.stub(dbUtils, "addNewJob").callsFake(() => {});

    // Stub fileutil calls
    goodExternalLinkBuffer = sinon.fake();
    badMimeExternalLinkBuffer = sinon.fake();
    badDimensionsBuffer = sinon.fake();
    sinon.stub(fileUtils, "retrieveFile").callsFake((link: string) => {
      if (link.startsWith("good_link")) {
        return goodExternalLinkBuffer;
      } else if (link === "bad_link") {
        throw new Error("erroring out on purpose for testing");
      } else if (link === "bad_mime_type") {
        return badMimeExternalLinkBuffer;
      } else if (link === "weird_link") {
        return weirdLinkBuffer;
      } else if (link === "zip_link") {
        return fs.readFileSync("test/resources/zip_with_two_images.zip");
      } else if (link === "bad_dimensions") {
        return badDimensionsBuffer;
      }
    });

    sinon.stub(fileUtils, "isAllowedMimeType").callsFake(buffer => {
      if (buffer === badMimeExternalLinkBuffer) {
        return false;
      } else if (buffer === weirdLinkBuffer) {
        throw new Error();
      }
      return true;
    });

    sinon.stub(fileUtils, "hasValidDimensions").callsFake(buffer => {
      if (buffer === badDimensionsBuffer) {
        return false;
      }
      return true;
    });

    sinon.stub(fileUtils, "getImageInfo").callsFake(buffer => {
      return { width: 500, height: 500, type: "jpg" };
    });

    // Stub file-type
    gifBuffer = sinon.fake();
    fileTypeStub = sinon.stub(fileType, 'default').callsFake((buffer) => {
      if (buffer === gifBuffer) {
        return { ext: 'gif', mime: 'image/gif' };
      }
      return { ext: 'png', mime: 'image/png' };
    });
  });

  beforeEach(() => {
    fileUtilsDeleteObjectsFromS3Stub = sinon
      .stub(fileUtils, "deleteObjectsFromS3")
      .callsFake(() => {});
    fileUtilsUploadToS3Stub = sinon
      .stub(fileUtils, "uploadToS3")
      .callsFake(() => {});
    fileUtilsWaitForFileToExist = sinon
      .stub(fileUtils, "waitForFileToExist")
      .callsFake(() => {});
    dbUtilsUpdateJobStub = sinon.stub(dbUtils, "updateJob").callsFake(() => {});
    dbUtilsGetItemJob = sinon
      .stub(dbUtils, "getItem")
      .callsFake((region, tableName, id) => {
        if (id === "done_job_id") {
          return {
            Item: {
              jobResponse: "response",
              jobStatus: "DONE"
            }
          };
        } else if (id === "processing_job_id") {
          return {
            Item: {
              jobResponse: "response",
              jobStatus: "PROCESSING"
            }
          };
        } else if (id === "not_found_id") {
          return {};
        }
      });
  });

  afterEach(() => {
    dbUtilsUpdateJobStub.restore();
    dbUtilsUpdateJobStub.reset();

    fileUtilsUploadToS3Stub.restore();
    fileUtilsUploadToS3Stub.reset();

    fileUtilsWaitForFileToExist.restore();
    fileUtilsWaitForFileToExist.reset();

    fileUtilsDeleteObjectsFromS3Stub.restore();
    fileUtilsDeleteObjectsFromS3Stub.reset();

    dbUtilsGetItemJob.restore();
    dbUtilsGetItemJob.reset();

    fileTypeStub.restore();
    fileTypeStub.reset();
  });

  describe("start image processing job", () => {
    it("should return uuid of the job, if request is valid", async () => {
      const fakeRequest = {};
      const newJobUuid = await processimagesService.startImageProcessingJob(
        fakeRequest
      );
      expect(newJobUuid).is.eql(testUuid);
    });
  });

  describe("process images", () => {
    it("should save images in s3 and response in dynamo, if external images processed successfully", async () => {
      const fakeRequest = {
        external_images: [
          { location: "good_link" },
          { location: "good_link2" },
          { location: "good_link3" }
        ]
      };
      const expectedResponse = {
        good_link: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        good_link2: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        good_link3: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(6);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record retrieval failure in dynamo, if external image does not exist", async () => {
      const fakeRequest = {
        external_images: [{ location: "bad_link" }]
      };
      const expectedResponse = {
        bad_link: PROCESSING_ERROR_CODES.UNABLE_TO_RETRIEVE
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record mimecheck failure in dynamo, if external image has bad mimetype", async () => {
      const fakeRequest = {
        external_images: [{ location: "bad_mime_type" }]
      };
      const expectedResponse = {
        bad_mime_type: PROCESSING_ERROR_CODES.INVALID_MIME_TYPE
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record dimensions failure in dynamo, if external image has bad dimensions", async () => {
      const fakeRequest = {
        external_images: [{ location: "bad_dimensions" }]
      };
      const expectedResponse = {
        bad_dimensions: PROCESSING_ERROR_CODES.INVALID_IMAGE_DIMENSIONS
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record unexpected failure in dynamo, if external image failed to process", async () => {
      const fakeRequest = {
        external_images: [{ location: "weird_link" }]
      };
      const expectedResponse = {
        weird_link: PROCESSING_ERROR_CODES.UNEXPECTED_ERROR
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should attempt to delete successfully processed images from s3, if one of the images failed and the other one succeeded", async () => {
      const fakeRequest = {
        external_images: [{ location: "good_link" }, { location: "bad_link" }]
      };
      const expectedResponse = {
        good_link: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        bad_link: PROCESSING_ERROR_CODES.UNABLE_TO_RETRIEVE
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );

      expect(fileUtilsDeleteObjectsFromS3Stub.callCount).to.be.eql(1);
      expect(fileUtilsDeleteObjectsFromS3Stub.getCall(0).args[2]).to.be.eql([
        "raw-images/test-uuid",
        "processed-images/test-uuid"
      ]);
    });

    it("should save files in zip in s3 and response in dynamo, if all images in zip file are valid", async () => {
      const fakeRequest = {
        zip_file: { location: "zip_link" }
      };

      const expectedResponse = {
        zip_file_processing_metadata: {
          success: true
        },
        "image1.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        "image2.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(4);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("with files to process - should save files in zip in s3 and response in dynamo, if all images in zip file are valid", async () => {
      const fakeRequest = {
        zip_file: {
          location: "zip_link",
          files_to_process: ["image1.png", "image2.png"]
        }
      };

      const expectedResponse = {
        zip_file_processing_metadata: {
          success: true
        },
        "image1.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        "image2.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(4);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record retrieval failure in dynamo, if zip file does not exist", async () => {
      const fakeRequest = {
        zip_file: { location: "bad_link" }
      };

      const expectedResponse = {
        zip_file_processing_metadata: PROCESSING_ERROR_CODES.UNABLE_TO_RETRIEVE
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(0);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should record external file and files inside zip in dynamo, if both external link and zip file was requested for processing", async () => {
      const fakeRequest = {
        zip_file: { location: "zip_link" },
        external_images: [{ location: "good_link" }]
      };

      const expectedResponse = {
        zip_file_processing_metadata: {
          success: true
        },
        "image1.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        "image2.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        good_link: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };
      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(6);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("with files to process - should ignore files not in files to process and should record external file and files inside zip in dynamo", async () => {
      const fakeRequest = {
        zip_file: {
          location: "zip_link",
          files_to_process: ["image1.png"]
        }
      };

      const expectedResponse = {
        zip_file_processing_metadata: {
          success: true
        },
        "image1.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(2);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });
    it("with files to process - should record external file and files inside zip in dynamo, if both external link and zip file was requested for processing", async () => {
      const fakeRequest = {
        zip_file: {
          location: "zip_link",
          files_to_process: ["image1.png", "image2.png", "imageNotInZip.png"]
        },
        external_images: [{ location: "good_link" }]
      };

      const expectedResponse = {
        zip_file_processing_metadata: {
          success: true
        },
        "imageNotInZip.png": {
          error_code: "IMAGE_NOT_FOUND_IN_ZIP",
          message: "Image not included in zip.",
          success: false
        },
        "image1.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        "image2.png": {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        },
        good_link: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };

      await processimagesService.processRequest(fakeRequest, testUuid);

      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.getCall(1).args[3]).to.be.eql(
        "processed-images/test-uuid"
      );
      expect(fileUtilsUploadToS3Stub.callCount).to.be.eql(6);

      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(
        expectedResponse
      );
    });

    it("should skip imgix processing for GIF files to preserve animation", async () => {
      const fakeRequest = {
        external_images: [{ location: "gif_link" }]
      };
      
      // Override the retrieveFile stub for this test
      const retrieveFileStub = fileUtils.retrieveFile as sinon.SinonStub;
      retrieveFileStub.withArgs("gif_link").returns(gifBuffer);
      
      const expectedResponse = {
        gif_link: {
          raw_s3_location:
            "https://s3.amazonaws.com/bucketName/raw-images/test-uuid",
          processed_s3_location:
            "https://s3.amazonaws.com/bucketName/processed-images/test-uuid",
          success: true
        }
      };
    
      await processimagesService.processRequest(fakeRequest, testUuid);
    
      // Verify that the raw image was uploaded to S3
      expect(fileUtilsUploadToS3Stub.callCount).to.be.at.least(2);
      expect(fileUtilsUploadToS3Stub.getCall(0).args[3]).to.be.eql(
        "raw-images/test-uuid"
      );
      
      // Verify that the processed image is the same as the raw image (no imgix processing)
      expect(fileUtilsUploadToS3Stub.getCall(1).args[1]).to.be.eql(gifBuffer);
      
      // Verify that the job was updated in DynamoDB
      expect(dbUtilsUpdateJobStub.callCount).to.be.eql(1);
      expect(dbUtilsUpdateJobStub.getCall(0).args[2]).to.be.eql("test-uuid");
      expect(dbUtilsUpdateJobStub.getCall(0).args[3]).to.be.eql("DONE");
      expect(dbUtilsUpdateJobStub.getCall(0).args[4]).to.be.eql(expectedResponse);
      
      // Verify that retrieveFile was not called with an imgix URL
      const imgixCalls = retrieveFileStub.getCalls().filter(call => 
        call.args[0] && call.args[0].includes('imgix.net')
      );
      expect(imgixCalls.length).to.be.eql(0);
    });
  });

  describe("get job response", () => {
    it("should throw not found error, if jobId does not exist in dynamo", async () => {
      try {
        await processimagesService.getJobResponse("not_found_id");
      } catch (error) {
        expect(error).instanceOf(errors.NotFoundError);
        expect(error.body).to.be.not.undefined;
      }
    });

    it("should throw bad request error, if the job has not finished processing", async () => {
      try {
        await processimagesService.getJobResponse("processing_job_id");
      } catch (error) {
        expect(error).instanceOf(errors.BadRequestError);
        expect(error.body).to.be.not.undefined;
      }
    });

    it("should return response of the job, if the job finished processing", async () => {
      const response = await processimagesService.getJobResponse("done_job_id");
      expect(response).to.be.eql("response");
    });
  });
});

