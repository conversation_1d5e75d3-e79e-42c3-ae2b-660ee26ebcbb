import { expect } from "chai";
import * as fileUtils from "../../../src/utils/file.utils";
import fs = require("fs");

describe("file.utils", () => {
  describe("parseMimeType", () => {
    const allowedMimeTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/svg+xml",
      "image/tiff"
    ];

    it("should identify image file and return proper extension", () => {
      // Load the actual Bmo.jpg file from test resources
      // Note: The file-type library may detect this differently than expected
      const imageBuffer = fs.readFileSync("test/resources/Bmo.jpg");

      const result = fileUtils.parseMimeType(imageBuffer, allowedMimeTypes);

      expect(result.allowed).to.be.true;
      expect(result.mimeType).to.be.equal("image/jpeg");
      expect(result.extension).to.be.equal("jpg");
    });

    it("should return false for unrecognized file type", () => {
      // Use a text file that should not be recognized as an image
      const textBuffer = fs.readFileSync("test/resources/bad_file.txt");

      const result = fileUtils.parseMimeType(textBuffer, allowedMimeTypes);

      expect(result.allowed).to.be.false;
      expect(result.mimeType).to.be.undefined;
      expect(result.extension).to.be.undefined;
    });

  });
});
